# ADC3智能采样功能说明

## 功能概述

按下第八个按钮后，系统会对ADC3采样信号进行智能采样，包括：

1. **基频确定**：使用FFT分析确定输入信号的基频
2. **采样率调整**：根据基频调整ADC3的采样率
3. **整数周期采样**：确保采样整数个信号周期
4. **谐波分析**：使用新的采样数据进行谐波分析

## 实现原理

### 1. 基频确定 (ADC3_DetermineFundamentalFrequency)

- 使用当前采样率（815534Hz）对ADC3进行4096点采样
- 对采样数据进行FFT分析
- 应用汉宁窗减少频谱泄漏
- 寻找频谱中的最大峰值（排除直流分量）
- 计算对应的基频：`基频 = (峰值索引 × 采样率) / FFT长度`

### 2. 采样率调整 (ADC3_SetSamplingRate)

根据检测到的基频，计算最优采样率：

```
目标采样点数 = 2048 或 4096（根据基频选择）
目标周期数 = 4-8个周期（根据基频调整）
所需采样率 = (目标采样点数 × 基频) / 目标周期数
```

采样率限制：
- 最小：10kHz
- 最大：2MHz

### 3. 定时器频率更新 (TIM3_UpdateFrequency)

动态调整TIM3定时器频率来改变ADC采样率：

```
定时器周期 = 84MHz / 目标采样率
```

### 4. 智能采样流程 (ADC3_SmartSampling)

1. 使用默认采样率进行初始采样
2. 通过FFT分析确定基频
3. 根据基频计算最优采样率
4. 更新TIM3频率
5. 重新采样并进行谐波分析

## 采样参数选择

### 采样点数选择
- 基频 < 100Hz：使用4096点（更好的频率分辨率）
- 基频 ≥ 100Hz：使用2048点（减少计算量）

### 周期数选择
- 基频 > 10kHz：采样4个周期
- 基频 > 5kHz：采样6个周期
- 其他情况：采样8个周期

## 使用方法

1. 连接待测信号到ADC3输入端（PF7引脚）
2. 按PE4键选择第八个按钮（ADC3 OFF）
3. 按PE3键激活智能采样功能
4. 系统自动完成基频检测和采样率调整
5. 在串口和LCD上查看结果

## 输出信息

### 串口输出
- 基频检测结果
- 采样率调整信息
- 谐波分析结果

### LCD显示
- 第一行：基频信息
- 第二行：采样参数（采样率、采样点数、周期数）

## 注意事项

1. 确保输入信号有足够的幅度和信噪比
2. 基频检测依赖于信号的周期性
3. 采样率调整可能影响其他使用TIM3的功能
4. 建议在相对稳定的信号上使用此功能

## 技术参数

- FFT长度：2048或4096点
- 频率分辨率：采样率/FFT长度
- 基频检测范围：约12Hz - 400kHz
- 采样率范围：10kHz - 2MHz
- 定时器精度：84MHz时钟源
